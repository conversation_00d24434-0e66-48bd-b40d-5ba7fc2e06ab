.main-content-panel {
  flex: 1;
  height: 100vh;
  min-height: 0;
  overflow: hidden;
  padding: 20px;
  box-sizing: border-box;
  background-color: #f8f9fa;
  display: flex;
  flex-direction: column;
  width: 100%;
  position: relative;
  z-index: 1;
}

.lesson-content-body {
  flex: 1;
  min-height: 0;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  /* Allow ONLY vertical scrolling for the content */
  overflow-x: hidden;
}

/* --- FIX: Ensure content fits within the panel --- */
.lesson-content {
  width: 100%;
  max-width: 100%;
  height: 100%;
  position: relative;
  z-index: 1;
}

.lesson-content img,
.lesson-content svg {
  max-width: 100%;
  height: auto;
  display: block;
}

/* --- END FIX --- */


.audio-player {
  border: 1px solid #e0e0e0;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 20px;
  /* Reduced margin slightly */
  transition: all 0.3s ease;
  background-color: white;
  flex-shrink: 0;
  /* Prevent the player from shrinking */
}

/* --- All other styles in maincontent.css remain the same --- */

.audio-player.disabled {
  opacity: 0.5;
  background-color: rgba(255, 255, 255, 0.3);
}

.audio-player.disabled .play-pause-btn-main {
  cursor: not-allowed;
}

.audio-player.error {
  border-color: #f44336;
  background-color: #fff5f5;
}

.player-top-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.audio-title {
  flex: 1;
  margin: 0;
  font-weight: bold;
  color: #333;
  font-size: 14px;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.track-counter {
  font-size: 12px;
  color: #666;
  font-weight: normal;
}

.audio-controls {
  display: flex;
  align-items: center;
  gap: 8px;
}

.play-pause-btn-main {
  background: #333;
  color: white;
  border: none;
  border-radius: 50%;
  width: 32px;
  height: 32px;
  font-size: 16px;
  line-height: 32px;
  cursor: pointer;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
}

.track-nav-btn {
  background: #6c757d;
  color: white;
  border: none;
  border-radius: 4px;
  width: 28px;
  height: 28px;
  font-size: 12px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s;
}

.track-nav-btn:hover:not(:disabled) {
  background: #545b62;
}

.track-nav-btn:disabled {
  background: #ccc;
  cursor: not-allowed;
  opacity: 0.5;
}

.player-bottom-row {
  display: flex;
  align-items: center;
  gap: 15px;
  font-size: 14px;
  color: #666;
}

.time-display {
  min-width: 40px;
  text-align: center;
  font-family: "Courier New", monospace;
  font-size: 12px;
}

.progress-bar-container {
  flex-grow: 1;
  height: 20px;
  cursor: pointer;
  display: flex;
  align-items: center;
  padding: 7px 0;
  position: relative;
}

.progress-bar-track {
  width: 100%;
  height: 6px;
  background-color: #eee;
  border-radius: 3px;
  position: relative;
  overflow: hidden;
}

.progress-bar-current {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  background-color: #333;
  border-radius: 3px;
  transition: width 0.1s ease;
}

.text-size-small {
  font-size: 14px;
}

.text-size-normal {
  font-size: 16px;
}

.text-size-large {
  font-size: 18px;
}

.text-size-small .lesson-content h1 {
  font-size: 18px;
}

.text-size-normal .lesson-content h1 {
  font-size: 20px;
}

.text-size-large .lesson-content h1 {
  font-size: 22px;
}