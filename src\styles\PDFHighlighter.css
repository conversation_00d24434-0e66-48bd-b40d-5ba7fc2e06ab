/* PDF Highlighter Container */
.pdf-highlighter-container {
  width: 100%;
  height: 100%;
  min-height: 600px;
  display: flex;
  flex-direction: column;
  background-color: #f8f9fa;
  flex: 1;
}

/* Loading and Error States */
.pdf-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  font-size: 16px;
  color: #666;
  background-color: #f8f9fa;
}

.pdf-error {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 200px;
  color: #dc3545;
  text-align: center;
  background-color: #f8f9fa;
}

/* Highlight Popup Styles */
.highlight-popup {
  background: white;
  border-radius: 6px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  padding: 8px 12px;
  max-width: 200px;
  border: 1px solid #e0e0e0;
}

.highlight-comment {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 14px;
  line-height: 1.4;
}

.comment-emoji {
  font-size: 16px;
  flex-shrink: 0;
}

.comment-text {
  color: #333;
  word-break: break-word;
}

/* Text Size Responsive Scaling */
.pdf-highlighter-container.small {
  font-size: 0.9em;
}

.pdf-highlighter-container.large {
  font-size: 1.1em;
}

/* Override react-pdf-highlighter styles for better integration */
.PdfHighlighter {
  height: 100% !important;
}

.PdfHighlighter__container {
  background-color: #f8f9fa !important;
}

/* Tip (selection tooltip) styling */
.Tip {
  background: white !important;
  border: 1px solid #e0e0e0 !important;
  border-radius: 6px !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
  padding: 12px !important;
}

.Tip__card {
  background: transparent !important;
  border: none !important;
  box-shadow: none !important;
}

.Tip__input {
  border: 1px solid #ddd !important;
  border-radius: 4px !important;
  padding: 8px !important;
  font-size: 14px !important;
  margin-bottom: 8px !important;
  width: 200px !important;
}

.Tip__input:focus {
  outline: none !important;
  border-color: #4a90e2 !important;
  box-shadow: 0 0 0 2px rgba(74, 144, 226, 0.2) !important;
}

.Tip__button {
  background-color: #4a90e2 !important;
  color: white !important;
  border: none !important;
  border-radius: 4px !important;
  padding: 6px 12px !important;
  font-size: 14px !important;
  cursor: pointer !important;
  transition: background-color 0.2s !important;
}

.Tip__button:hover {
  background-color: #357abd !important;
}

/* Highlight styles with color support */
.highlight-yellow .Highlight__part { background-color: #ffeb3b !important; }
.highlight-green  .Highlight__part { background-color: #8bc34a !important; }
.highlight-blue   .Highlight__part { background-color: #90caf9 !important; }
.highlight-pink   .Highlight__part { background-color: #f48fb1 !important; }
.highlight-purple .Highlight__part { background-color: #ce93d8 !important; }
.highlight-orange .Highlight__part { background-color: #ffb74d !important; }
.highlight-red    .Highlight__part { background-color: #ef9a9a !important; }

.Highlight {
  transition: opacity 0.2s ease !important;
}

.Highlight:hover {
  opacity: 0.8 !important;
}

.Highlight__part {
  border-radius: 2px !important;
  transition: all 0.2s ease !important;
}

/* Area highlight styles */
.AreaHighlight {
  border: 2px solid #4a90e2 !important;
  border-radius: 4px !important;
  background-color: rgba(74, 144, 226, 0.1) !important;
  transition: all 0.2s ease !important;
}

.AreaHighlight:hover {
  border-color: #357abd !important;
  background-color: rgba(74, 144, 226, 0.2) !important;
}

/* Popup styles */
.Popup {
  z-index: 10 !important;
}

/* Responsive design */
@media (max-width: 768px) {
  .pdf-highlighter-container {
    padding: 10px;
  }

  .highlight-popup {
    max-width: 150px;
    padding: 6px 8px;
  }

  .highlight-comment {
    font-size: 12px;
  }

  .Tip__input {
    width: 150px !important;
    font-size: 12px !important;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .highlight-popup {
    border-width: 2px;
    border-color: #000;
  }

  .Highlight__part {
    border: 1px solid !important;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {

  .Highlight,
  .Highlight__part,
  .AreaHighlight,
  .Tip__button {
    transition: none !important;
  }
}

/* Dark mode support (if needed) */
@media (prefers-color-scheme: dark) {
  .pdf-highlighter-container {
    background-color: #1a1a1a;
  }

  .pdf-loading,
  .pdf-error {
    background-color: #1a1a1a;
    color: #e0e0e0;
  }

  .highlight-popup {
    background: #2d2d2d;
    border-color: #444;
    color: #e0e0e0;
  }

  .Tip {
    background: #2d2d2d !important;
    border-color: #444 !important;
    color: #e0e0e0 !important;
  }

  .Tip__input {
    background: #1a1a1a !important;
    border-color: #444 !important;
    color: #e0e0e0 !important;
  }
}

/* Overlapping Highlights Support */
.PdfHighlighter__highlight-layer {
  position: relative;
  pointer-events: none;
}

.PdfHighlighter__highlight-layer > * {
  pointer-events: auto;
}

/* Enhanced highlight styles for better overlap handling */
.PdfHighlighter__highlight {
  position: relative;
  transition: opacity 0.2s ease;
  mix-blend-mode: normal; /* Use normal blend mode for better visibility */
  opacity: 0.6;
  z-index: 1000;
}

.PdfHighlighter__highlight:hover {
  opacity: 0.8;
  z-index: 9999;
}

/* Improved highlight layering for overlaps */
.PdfHighlighter__highlight[data-highlight-id] {
  mix-blend-mode: normal;
  opacity: 0.6;
}

.PdfHighlighter__highlight[data-highlight-id]:hover {
  opacity: 0.8;
  z-index: 9999;
}

/* Selection state handling */
.PdfHighlighter__highlight[data-selection-disabled="true"] {
  pointer-events: none;
  user-select: none;
  opacity: 0.3;
}

/* Aggressive hiding during selection */
.PdfHighlighter__highlight[data-aggressive-disabled="true"] {
  display: none !important;
}

/* Split highlights - ensure proper rendering */
.PdfHighlighter__highlight[data-highlight-id*="_split_"] {
  mix-blend-mode: normal;
  opacity: 0.6;
  border: 1px solid rgba(0,0,0,0.1); /* Add subtle border for better visibility */
}

.PdfHighlighter__highlight[data-highlight-id*="_split_"]:hover {
  opacity: 0.8;
  z-index: 9999;
}

/* Segmented highlights - ensure proper rendering */
.PdfHighlighter__highlight[data-highlight-id*="_segment_"] {
  mix-blend-mode: normal;
  opacity: 0.6;
}

.PdfHighlighter__highlight[data-highlight-id*="_segment_"]:hover {
  opacity: 0.8;
  z-index: 9999;
}

/* Highlight color blending for overlaps */
.PdfHighlighter__highlight {
  background-blend-mode: multiply;
}

/* Improved tooltip positioning for overlapping highlights */
.Popup {
  z-index: 10000;
  position: relative;
}

/* Better selection handling */
.PdfHighlighter__selection {
  background-color: rgba(0, 123, 255, 0.3);
  z-index: 9998;
}

/* Enhanced tip positioning */
.Tip {
  z-index: 10001;
  position: relative;
}

/* Tip styles for better UX */
.PdfHighlighter__tip {
  z-index: 10000;
  background: white;
  border-radius: 6px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border: 1px solid #e0e0e0;
  padding: 8px;
}

/* Prevent text selection interference */
.pdf-highlighter-container ::selection {
  background: rgba(0, 123, 255, 0.3);
}

.pdf-highlighter-container ::-moz-selection {
  background: rgba(0, 123, 255, 0.3);
}