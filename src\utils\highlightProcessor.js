/**
 * Utility for checking if highlights need processing for overlaps
 */

/**
 * Check if highlights need processing (have overlaps)
 */
export const needsProcessing = (highlights) => {
  if (!highlights || highlights.length < 2) return false;
  
  // Don't process if ALL highlights are already segments (prevent infinite loops)
  const segmentHighlights = highlights.filter(h => h.id.includes('_segment_') || h.id.includes('_split_'));
  const originalHighlights = highlights.filter(h => !h.id.includes('_segment_') && !h.id.includes('_split_'));
  
  // If we only have segments and no new original highlights, skip processing
  if (segmentHighlights.length > 0 && originalHighlights.length === 0) {
    console.log('Only segments found, skipping processing');
    return false;
  }
  
  // If we have both segments and new original highlights, we might need processing
  // If we only have original highlights, we might need processing
  
  // Check if we have enough original highlights to process
  if (originalHighlights.length < 2) return false;
  
  // Quick check for overlaps
  for (let i = 0; i < originalHighlights.length; i++) {
    for (let j = i + 1; j < originalHighlights.length; j++) {
      const h1 = originalHighlights[i];
      const h2 = originalHighlights[j];
      
      // Skip self-comparison
      if (h1.id === h2.id) continue;
      
      if (h1.position?.pageNumber === h2.position?.pageNumber &&
          h1.position?.boundingRect && h2.position?.boundingRect) {
        
        const rect1 = h1.position.boundingRect;
        const rect2 = h2.position.boundingRect;
        
        // Check for overlap
        const overlaps = !(
          rect1.x2 < rect2.x1 || 
          rect2.x2 < rect1.x1 || 
          rect1.y2 < rect2.y1 || 
          rect2.y2 < rect1.y1
        );
        
        if (overlaps) {
          console.log(`Overlap detected between ${h1.content?.text} and ${h2.content?.text}`);
          return true;
        }
      }
    }
  }
  
  return false;
};
