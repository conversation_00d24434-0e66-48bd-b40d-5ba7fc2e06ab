import React, { useCallback, useEffect, useRef } from 'react';
import { PdfHighlighter } from 'react-pdf-highlighter';

/**
 * Simplified custom PdfHighlighter that focuses on fixing the selection issue
 * without over-complicating the solution
 */
const SimpleCustomPdfHighlighter = ({ 
  pdfDocument, 
  highlights, 
  onSelectionFinished, 
  highlightTransform,
  ...props 
}) => {
  const isSelectingRef = useRef(false);

  // Simple selection handler that temporarily disables highlight pointer events
  const handleSelectionStart = useCallback(() => {
    if (isSelectingRef.current) return;
    
    isSelectingRef.current = true;
    
    // Temporarily disable pointer events on highlights
    const highlightElements = document.querySelectorAll(
      '.PdfHighlighter__highlight, .Highlight__part, .Highlight'
    );
    highlightElements.forEach(el => {
      el.style.pointerEvents = 'none';
      el.setAttribute('data-temp-disabled', 'true');
    });
    
    console.log('Temporarily disabled highlight interactions for selection');
  }, []);

  const handleSelectionEnd = useCallback(() => {
    if (!isSelectingRef.current) return;
    
    // Re-enable pointer events after a short delay
    setTimeout(() => {
      const highlightElements = document.querySelectorAll('[data-temp-disabled="true"]');
      highlightElements.forEach(el => {
        el.style.pointerEvents = 'auto';
        el.removeAttribute('data-temp-disabled');
      });
      
      isSelectingRef.current = false;
      console.log('Re-enabled highlight interactions');
    }, 100);
  }, []);

  // Enhanced selection finished handler
  const enhancedOnSelectionFinished = useCallback((position, content, hideTipAndSelection, transformSelection) => {
    // Ensure highlights are re-enabled
    handleSelectionEnd();
    
    // Call the original handler
    return onSelectionFinished(position, content, hideTipAndSelection, transformSelection);
  }, [onSelectionFinished, handleSelectionEnd]);

  // Simple event handling
  useEffect(() => {
    const handleMouseDown = (e) => {
      // Only handle text selection, not highlight clicks
      if (!e.target.closest('.PdfHighlighter__highlight') && 
          !e.target.closest('.Highlight__part') && 
          !e.target.closest('.Highlight') &&
          !e.target.closest('.Popup') &&
          !e.target.closest('.Tip')) {
        handleSelectionStart();
      }
    };

    const handleMouseUp = () => {
      if (isSelectingRef.current) {
        handleSelectionEnd();
      }
    };

    // Add event listeners
    document.addEventListener('mousedown', handleMouseDown);
    document.addEventListener('mouseup', handleMouseUp);

    return () => {
      document.removeEventListener('mousedown', handleMouseDown);
      document.removeEventListener('mouseup', handleMouseUp);
      
      // Cleanup on unmount
      const highlightElements = document.querySelectorAll('[data-temp-disabled="true"]');
      highlightElements.forEach(el => {
        el.style.pointerEvents = 'auto';
        el.removeAttribute('data-temp-disabled');
      });
    };
  }, [handleSelectionStart, handleSelectionEnd]);

  return (
    <PdfHighlighter
      pdfDocument={pdfDocument}
      highlights={highlights}
      onSelectionFinished={enhancedOnSelectionFinished}
      highlightTransform={highlightTransform}
      {...props}
    />
  );
};

export default SimpleCustomPdfHighlighter;
