// Application Constants
export const COLORS = {
  BACKGROUND: "#FFFBF3",
  PRIMARY: "#333",
  SECONDARY: "#555",
  BORDER: "#e0e0e0",
  TEXT: "#444",
  HOVER: "#f5f2eb",
  DISABLED: "rgba(255, 255, 255, 0.3)",
};

export const TEXT_SIZES = {
  SMALL: "small",
  NORMAL: "normal",
  LARGE: "large",
};

export const FONT_SIZES = {
  [TEXT_SIZES.SMALL]: {
    base: "14px",
    heading: "16px",
  },
  [TEXT_SIZES.NORMAL]: {
    base: "16px",
    heading: "18px",
  },
  [TEXT_SIZES.LARGE]: {
    base: "18px",
    heading: "20px",
  },
};

export const AUDIO_STATES = {
  IDLE: "idle",
  LOADING: "loading",
  PLAYING: "playing",
  PAUSED: "paused",
  ERROR: "error",
};

// Highlighting Constants
export const DEFAULT_HIGHLIGHT_COLOR = {
  id: 'yellow',
  name: 'Yellow',
  backgroundColor: '#ffeb3b',
  color: '#f57f17'
};

export const HIGHLIGHT_COLORS = [
  {
    id: 'yellow',
    name: 'Yellow',
    backgroundColor: '#ffeb3b',
    color: '#f57f17'
  },
  {
    id: 'green',
    name: 'Green',
    backgroundColor: '#8bc34a',
    color: '#33691e'
  },
  {
    id: 'blue',
    name: 'Blue',
    backgroundColor: '#90caf9',
    color: '#1565c0'
  },
  {
    id: 'pink',
    name: 'Pink',
    backgroundColor: '#f48fb1',
    color: '#ad1457'
  },
  {
    id: 'purple',
    name: 'Purple',
    backgroundColor: '#ce93d8',
    color: '#6a1b9a'
  },
  {
    id: 'orange',
    name: 'Orange',
    backgroundColor: '#ffb74d',
    color: '#e65100'
  },
  {
    id: 'red',
    name: 'Red',
    backgroundColor: '#ef9a9a',
    color: '#c62828'
  }
];
// export const DEFAULT_HIGHLIGHT_COLOR = HIGHLIGHT_COLORS.YELLOW;

export const ERROR_MESSAGES = {
  AUDIO_LOAD_FAILED: "Failed to load audio file",
  AUDIO_PLAY_FAILED: "Failed to play audio",
  AUDIO_NOT_SUPPORTED: "Audio format not supported",
  NETWORK_ERROR: "Network error occurred",
  GENERIC_ERROR: "An unexpected error occurred",
};

export const STORAGE_KEYS = {
  TEXT_SIZE: "textSize",
  AUDIO_VOLUME: "audioVolume",
  LAST_TOPIC: "lastTopic",
};

export const DEBOUNCE_DELAYS = {
  SEARCH: 300,
  RESIZE: 150,
  SCROLL: 100,
};

export const PAGINATION = {
  DEFAULT_PAGE: 1,
  ITEMS_PER_PAGE: 10,
};

export const ACCESSIBILITY = {
  FOCUS_VISIBLE_OUTLINE: "2px solid #4A90E2",
  MIN_TOUCH_TARGET: "44px",
  HIGH_CONTRAST_RATIO: 4.5,
};

export const BREAKPOINTS = {
  MOBILE: "768px",
  TABLET: "1024px",
  DESKTOP: "1200px",
};

export const ANIMATION_DURATIONS = {
  FAST: "0.15s",
  NORMAL: "0.3s",
  SLOW: "0.5s",
};

