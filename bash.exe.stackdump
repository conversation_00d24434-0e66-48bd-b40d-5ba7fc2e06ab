Stack trace:
Frame         Function      Args
0007FFFFB6B0  00021006118E (00021028DEE8, 000210272B3E, 0007FFFFB6B0, 0007FFFFA5B0) msys-2.0.dll+0x2118E
0007FFFFB6B0  0002100469BA (000000000000, 000000000000, 000000000000, 000000000004) msys-2.0.dll+0x69BA
0007FFFFB6B0  0002100469F2 (00021028DF99, 0007FFFFB568, 0007FFFFB6B0, 000000000000) msys-2.0.dll+0x69F2
0007FFFFB6B0  00021006A41E (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A41E
0007FFFFB6B0  00021006A545 (0007FFFFB6C0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A545
0001004F94B7  00021006B9A5 (0007FFFFB6C0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2B9A5
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFFA43D0000 ntdll.dll
7FFFA3750000 KERNEL32.DLL
7FFFA16E0000 KERNELBASE.dll
7FFFA35A0000 USER32.dll
7FFFA1E80000 win32u.dll
7FFFA3170000 GDI32.dll
7FFFA1FE0000 gdi32full.dll
7FFFA1DE0000 msvcp_win.dll
7FFFA1AD0000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFFA4180000 advapi32.dll
7FFFA3B40000 msvcrt.dll
7FFFA3A70000 sechost.dll
7FFFA1AA0000 bcrypt.dll
7FFFA4060000 RPCRT4.dll
7FFFA0E80000 CRYPTBASE.DLL
7FFFA1BF0000 bcryptPrimitives.dll
7FFFA3EB0000 IMM32.DLL
