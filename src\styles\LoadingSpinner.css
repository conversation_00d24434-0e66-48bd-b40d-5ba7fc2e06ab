/* Loading Spinner Styles */
.loading-spinner {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 12px;
}

.loading-spinner__circle {
  position: relative;
  display: inline-block;
  border-radius: 50%;
  border: 2px solid rgba(51, 51, 51, 0.1);
  border-top-color: #333;
  animation: spin 1s linear infinite;
}

.loading-spinner__inner {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  border: 2px solid transparent;
  border-top-color: rgba(51, 51, 51, 0.3);
  animation: spin 1.5s linear infinite reverse;
}

/* Size variants */
.loading-spinner--small .loading-spinner__circle {
  width: 20px;
  height: 20px;
}

.loading-spinner--medium .loading-spinner__circle {
  width: 32px;
  height: 32px;
}

.loading-spinner--large .loading-spinner__circle {
  width: 48px;
  height: 48px;
}

/* Color variants */
.loading-spinner--primary .loading-spinner__circle {
  border-top-color: #333;
}

.loading-spinner--secondary .loading-spinner__circle {
  border-top-color: #666;
}

.loading-spinner--light .loading-spinner__circle {
  border-color: rgba(255, 255, 255, 0.3);
  border-top-color: white;
}

.loading-spinner__message {
  font-family: "Montserrat", sans-serif;
  font-size: 14px;
  color: #666;
  text-align: center;
}

/* Overlay variant */
.loading-spinner-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 251, 243, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  backdrop-filter: blur(2px);
}

/* Inline Loader Styles */
.inline-loader {
  display: inline-flex;
  align-items: center;
  gap: 4px;
}

.inline-loader__dot {
  width: 6px;
  height: 6px;
  background-color: #333;
  border-radius: 50%;
  animation: pulse 1.4s ease-in-out infinite both;
}

.inline-loader__dot:nth-child(1) {
  animation-delay: -0.32s;
}

.inline-loader__dot:nth-child(2) {
  animation-delay: -0.16s;
}

.inline-loader--small .inline-loader__dot {
  width: 4px;
  height: 4px;
}

.inline-loader--medium .inline-loader__dot {
  width: 6px;
  height: 6px;
}

.inline-loader--large .inline-loader__dot {
  width: 8px;
  height: 8px;
}

/* Skeleton Loader Styles */
.skeleton-loader {
  background: linear-gradient(
    90deg,
    rgba(255, 255, 255, 0.2) 25%,
    rgba(255, 255, 255, 0.4) 50%,
    rgba(255, 255, 255, 0.2) 75%
  );
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
  background-color: #f0f0f0;
}

.text-skeleton {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.text-skeleton__line {
  margin-bottom: 0;
}

/* Audio Player Skeleton */
.audio-player-skeleton {
  border: 1px solid #e0e0e0;
  border-radius: 12px;
  padding: 16px;
  background-color: white;
}

.audio-player-skeleton__header {
  margin-bottom: 16px;
}

.audio-player-skeleton__controls {
  display: flex;
  align-items: center;
  gap: 12px;
}

/* Animations */
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes pulse {
  0%,
  80%,
  100% {
    transform: scale(0);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  .loading-spinner__circle,
  .loading-spinner__inner {
    animation: none;
  }
  
  .inline-loader__dot {
    animation: none;
    opacity: 0.7;
  }
  
  .skeleton-loader {
    animation: none;
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .loading-spinner__circle {
    border-color: transparent;
    border-top-color: currentColor;
  }
  
  .inline-loader__dot {
    background-color: currentColor;
  }
  
  .skeleton-loader {
    background-color: #ccc;
    background-image: none;
  }
}
