import React, { useCallback, useEffect, useMemo } from 'react';
import {
  AreaHighlight,
  Highlight,
  PdfLoader,
  Popup,
  Tip,
} from 'react-pdf-highlighter';
import 'react-pdf-highlighter/dist/style.css';
import { useAuthStore } from '../../stores/authStore';
import { useColorStore } from '../../stores/colorStore';
import { useHighlightStore } from '../../stores/highlightStore';
import SimpleCustomPdfHighlighter from './SimpleCustomPdfHighlighter';
import { processOverlappingHighlights } from '../../utils/highlightMerger';
import { needsProcessing } from '../../utils/highlightProcessor';
import '../../styles/PDFHighlighter.css';

const getNextId = () => String(Math.random()).slice(2);

const HighlightPopup = ({ comment }) => {
  return comment.text ? (
    <div className="highlight-popup">
      <div className="highlight-comment">
        {comment.emoji && <span className="comment-emoji">{comment.emoji}</span>}
        <span className="comment-text">{comment.text}</span>
      </div>
    </div>
  ) : null;
};

const PDFHighlighter = ({ pdfUrl, pageId, topicId, textSize }) => {
  const { selectedColor } = useColorStore();
  const { currentUser, isAuthenticated } = useAuthStore();
  const {
    isHighlightMode,
    getHighlights,
    addHighlight,
    updateHighlight,
    removeHighlight,
    _hasHydrated,
    setHasHydrated
  } = useHighlightStore();

  // Ensure hydration is complete
  useEffect(() => {
    if (!_hasHydrated) {
      // Force hydration if not already done
      const timeoutId = setTimeout(() => {
        setHasHydrated(true);
      }, 100);
      return () => clearTimeout(timeoutId);
    }
  }, [_hasHydrated, setHasHydrated]);

  // Get highlights for current page/topic (user-specific)
  const rawHighlights = (_hasHydrated && isAuthenticated && currentUser)
    ? getHighlights(pageId, topicId)
    : [];
  
  // Process overlapping highlights for rendering only
  const processedHighlights = useMemo(() => {
    return processOverlappingHighlights(rawHighlights);
  }, [rawHighlights]);
  
  // Handle store updates in useEffect to avoid render-time updates
  useEffect(() => {
    if (!rawHighlights || rawHighlights.length === 0) return;
    
    // Check if we need to process overlapping highlights
    if (needsProcessing(rawHighlights)) {
      console.log('Processing overlapping highlights and updating store...');
      
      const processed = processOverlappingHighlights(rawHighlights);
      
      // Group processed highlights by their original ID
      const segmentGroups = {};
      const standaloneHighlights = [];
      
      processed.forEach(highlight => {
        if (highlight.originalId) {
          if (!segmentGroups[highlight.originalId]) {
            segmentGroups[highlight.originalId] = [];
          }
          segmentGroups[highlight.originalId].push(highlight);
        } else {
          standaloneHighlights.push(highlight);
        }
      });
      
      // Only process if we actually have segments to create
      if (Object.keys(segmentGroups).length > 0) {
        // Update the store asynchronously
        setTimeout(() => {
          // For each original highlight that was segmented
          Object.entries(segmentGroups).forEach(([originalId, segments]) => {
            // Find the original highlight
            const originalHighlight = rawHighlights.find(h => h.id === originalId);
            
            if (originalHighlight && segments.length > 0) {
              // Remove the original highlight
              removeHighlight(pageId, topicId, originalId);
              
              // Add each segment as a new highlight
              segments.forEach((segment, index) => {
                // Calculate the approximate text for this segment
                const originalText = originalHighlight.content?.text || '';
                const originalRect = originalHighlight.position.boundingRect;
                const segmentRect = segment.position.boundingRect;
                
                // Estimate text portion based on x-coordinate ratio
                let segmentText = originalText;
                if (originalRect && segmentRect) {
                  const totalWidth = originalRect.x2 - originalRect.x1;
                  const segmentStart = (segmentRect.x1 - originalRect.x1) / totalWidth;
                  const segmentEnd = (segmentRect.x2 - originalRect.x1) / totalWidth;
                  
                  const startChar = Math.floor(segmentStart * originalText.length);
                  const endChar = Math.ceil(segmentEnd * originalText.length);
                  
                  segmentText = originalText.substring(startChar, endChar).trim();
                  if (!segmentText) segmentText = originalText; // Fallback
                }
                
                const newHighlight = {
                  ...segment,
                  id: `${originalId}_split_${index}_${Date.now()}`,
                  originalId: undefined,
                  createdAt: originalHighlight.createdAt,
                  comment: originalHighlight.comment,
                  userId: originalHighlight.userId,
                  content: {
                    ...originalHighlight.content,
                    text: segmentText
                  }
                };
                
                console.log(`Adding split highlight ${index}:`, {
                  originalText,
                  segmentText,
                  coordinates: segmentRect
                });
                
                addHighlight(pageId, topicId, newHighlight);
              });
            }
          });
        }, 100); // Small delay to ensure clean state updates
      }
    }
  }, [rawHighlights, pageId, topicId, addHighlight, removeHighlight]);
  
  const highlights = processedHighlights;

  // Debug logging
  useEffect(() => {
    console.log('PDFHighlighter Debug:', {
      _hasHydrated,
      isAuthenticated,
      currentUser: currentUser?.email,
      pageId,
      topicId,
      highlightsCount: highlights.length,
      isHighlightMode
    });
  }, [_hasHydrated, isAuthenticated, currentUser, pageId, topicId, highlights.length, isHighlightMode]);

  // Helper function to check if two rectangles overlap
  const doRectsOverlap = (rect1, rect2) => {
    return !(
      rect1.x2 < rect2.x1 || 
      rect2.x2 < rect1.x1 || 
      rect1.y2 < rect2.y1 || 
      rect2.y2 < rect1.y1
    );
  };

  // Helper function to check if two highlights overlap
  const doHighlightsOverlap = (highlight1, highlight2) => {
    if (highlight1.position.pageNumber !== highlight2.position.pageNumber) {
      return false;
    }
    return doRectsOverlap(highlight1.position.boundingRect, highlight2.position.boundingRect);
  };

  // Helper function to find overlapping highlights
  const findOverlappingHighlights = useCallback((newPosition, existingHighlights) => {
    return existingHighlights.filter(highlight => {
      if (highlight.position.pageNumber !== newPosition.pageNumber) {
        return false;
      }
      return doRectsOverlap(highlight.position.boundingRect, newPosition.boundingRect);
    });
  }, []);

  // Helper function to check if a selection overlaps with existing highlights
  const getOverlappingHighlights = useCallback((newPosition) => {
    return findOverlappingHighlights(newPosition, highlights);
  }, [highlights, findOverlappingHighlights]);

  // Custom selection handler to prevent highlight interference
  const handleCustomSelection = useCallback(() => {
    // Clear any existing highlights that might interfere with selection
    const selection = window.getSelection();
    if (selection && selection.rangeCount > 0) {
      const range = selection.getRangeAt(0);
      const selectedText = range.toString().trim();
      
      if (selectedText.length > 0) {
        console.log('Custom selection detected:', selectedText);
        
        // Remove any highlight elements that might interfere
        const highlightElements = document.querySelectorAll('.PdfHighlighter__highlight');
        highlightElements.forEach(el => {
          el.style.pointerEvents = 'none';
        });
        
        // Restore pointer events after a short delay
        setTimeout(() => {
          highlightElements.forEach(el => {
            el.style.pointerEvents = 'auto';
          });
        }, 100);
      }
    }
  }, []);

  // Add event listener for better selection handling
  useEffect(() => {
    const handleMouseUp = () => {
      handleCustomSelection();
    };

    document.addEventListener('mouseup', handleMouseUp);
    return () => {
      document.removeEventListener('mouseup', handleMouseUp);
    };
  }, [handleCustomSelection]);

  // Handle adding new highlights with vector position data and overlap detection
  const handleAddHighlight = useCallback((highlight) => {
    if (!isAuthenticated || !currentUser) {
      console.warn('User must be authenticated to add highlights');
      return;
    }

    console.log(`Adding highlight for user ${currentUser.username}:`, highlight);

    // Check for overlapping highlights
    const overlappingHighlights = getOverlappingHighlights(highlight.position);
    
    if (overlappingHighlights.length > 0) {
      console.log('Found overlapping highlights:', overlappingHighlights);
      // For now, we'll still add the highlight but with a warning
      // In a more sophisticated implementation, you might want to:
      // 1. Merge highlights
      // 2. Split highlights
      // 3. Ask user for confirmation
    }

    const newHighlight = {
      ...highlight,
      id: getNextId(),
      createdAt: new Date().toISOString(),
      content: highlight.content, // Preserve the selected text content
      position: {
        boundingRect: {
          x1: highlight.position.boundingRect.x1,
          y1: highlight.position.boundingRect.y1,
          x2: highlight.position.boundingRect.x2,
          y2: highlight.position.boundingRect.y2,
          width: highlight.position.boundingRect.width || 0,
          height: highlight.position.boundingRect.height || 0,
          pageNumber: highlight.position.boundingRect.pageNumber
        },
        rects: highlight.position.rects.map(rect => ({
          x1: rect.x1,
          y1: rect.y1,
          x2: rect.x2,
          y2: rect.y2,
          width: rect.width || 0,
          height: rect.height || 0,
          pageNumber: rect.pageNumber
        })),
        pageNumber: highlight.position.pageNumber
      },
      comment: {
        text: '',
        colorId: selectedColor?.id || 'yellow',
        emoji: '💡',
        color: selectedColor?.id || 'yellow',
        backgroundColor: selectedColor?.backgroundColor || '#ffeb3b',
      },
      // Add z-index for layering overlapping highlights
      zIndex: Date.now()
    };

    addHighlight(pageId, topicId, newHighlight);
  }, [addHighlight, pageId, topicId, selectedColor, isAuthenticated, currentUser, highlights]);

  // Handle updating highlights
  const handleUpdateHighlight = useCallback((highlightId, position, content) => {
    console.log('Updating highlight:', highlightId, position, content);
    updateHighlight(pageId, topicId, highlightId, position, content);
  }, [updateHighlight, pageId, topicId]);

  // Handle removing highlights (including segmented highlights)
  const handleRemoveHighlight = useCallback((highlightId) => {
    console.log('Removing highlight:', highlightId);
    
    // Check if this is a segmented highlight
    const originalId = highlightId.includes('_segment_') ? 
      highlightId.split('_segment_')[0] : highlightId;
    
    // Remove the original highlight (this will remove all segments)
    removeHighlight(pageId, topicId, originalId);
  }, [removeHighlight, pageId, topicId]);

  // Scale factor based on text size
  const scaleFactor = useMemo(() => {
    switch (textSize) {
      case 'small': return 0.8;
      case 'large': return 1.2;
      default: return 1.0;
    }
  }, [textSize]);

  if (!pdfUrl) {
    return (
      <div className="pdf-error">
        <p>No PDF URL provided</p>
      </div>
    );
  }

  // Check if user is authenticated
  if (!isAuthenticated || !currentUser) {
    return (
      <div className="pdf-error">
        <p>Please log in to view and create highlights</p>
      </div>
    );
  }

  // Show loading state until hydration is complete
  if (!_hasHydrated) {
    return (
      <div className="pdf-loading">
        <p>Loading highlights...</p>
      </div>
    );
  }

  return (
    <div className={`pdf-highlighter-container ${textSize}`}>
      <PdfLoader url={pdfUrl} beforeLoad={<div className="pdf-loading">Loading PDF...</div>}>
        {(pdfDocument) => (
          <SimpleCustomPdfHighlighter
            pdfDocument={pdfDocument}
            enableAreaSelection={(event) => event.altKey}
            onScrollChange={() => { }}
            scrollRef={() => { }}
            onSelectionFinished={(
              position,
              content,
              hideTipAndSelection,
              transformSelection
            ) => {
              // Custom selection handler to prevent interference with existing highlights
              const handleSelection = () => {
                // Clear any existing selection that might interfere
                if (window.getSelection) {
                  const selection = window.getSelection();
                  if (selection.rangeCount > 0) {
                    // Store the current selection
                    const range = selection.getRangeAt(0);
                    const selectedText = range.toString();
                    
                    // Only proceed if we have actual text selected
                    if (selectedText && selectedText.trim().length > 0) {
                      console.log('Selected text:', selectedText);
                      console.log('Selection position:', position);
                      
                      // Check if this selection overlaps with existing highlights
                      const overlapping = getOverlappingHighlights(position);
                      if (overlapping.length > 0) {
                        console.log('Selection overlaps with existing highlights:', overlapping);
                      }
                    }
                  }
                }
                
                // Call the original transform function
                transformSelection();
              };
              
              return (
                <Tip
                  onOpen={handleSelection}
                  onConfirm={(comment) => {
                    handleAddHighlight({
                      content,
                      position,
                      comment: {
                        text: comment.text || '',
                        colorId: selectedColor?.id || 'yellow',
                        emoji: comment.emoji || '💡',
                        color: selectedColor?.id || 'yellow',
                        backgroundColor: selectedColor?.backgroundColor || '#ffeb3b',
                      }
                    });
                    hideTipAndSelection();
                  }}
                />
              );
            }}
            highlightTransform={(
              highlight,
              index,
              setTip,
              hideTip,
              viewportToScaled,
              screenshot,
              isScrolledTo
            ) => {
              const isTextHighlight = !highlight.content?.image;

              // Calculate z-index based on highlight creation time and size
              // Newer highlights get higher z-index, smaller highlights get higher z-index
              const baseZIndex = 1000;
              const timeZIndex = highlight.zIndex || highlight.createdAt ? new Date(highlight.createdAt).getTime() : 0;
              const sizeZIndex = highlight.position?.boundingRect ? 
                -(highlight.position.boundingRect.width * highlight.position.boundingRect.height) : 0;
              const finalZIndex = baseZIndex + (timeZIndex % 1000) + (sizeZIndex / 10000);

              const component = isTextHighlight ? (
                <div 
                  className={`highlight-${highlight.comment?.colorId || highlight.comment?.id || highlight.comment?.color || 'yellow'}`}
                  style={{
                    position: 'relative',
                    zIndex: Math.floor(finalZIndex),
                    pointerEvents: 'auto'
                  }}
                >
                  <Highlight
                    isScrolledTo={isScrolledTo}
                    position={highlight.position}
                    comment={highlight.comment}
                    className={`highlight-${highlight.comment?.colorId || highlight.comment?.id || highlight.comment?.color || 'yellow'}`}
                    style={{ 
                      backgroundColor: highlight.comment?.backgroundColor,
                      opacity: 0.6, // Make overlapping highlights semi-transparent
                      position: 'relative',
                      zIndex: Math.floor(finalZIndex)
                    }}
                    onClick={(e) => {
                      e.stopPropagation();
                      // Enhanced removal with better UX
                      const confirmMessage = `Remove "${highlight.content?.text || 'this highlight'}"?`;
                      if (window.confirm(confirmMessage)) {
                        handleRemoveHighlight(highlight.id);
                      }
                    }}
                  />
                </div>
              ) : (
                <AreaHighlight
                  isScrolledTo={isScrolledTo}
                  highlight={highlight}
                  onChange={(boundingRect) => {
                    handleUpdateHighlight(
                      highlight.id,
                      { boundingRect: viewportToScaled(boundingRect) },
                      { image: screenshot(boundingRect) }
                    );
                  }}
                  style={{
                    zIndex: Math.floor(finalZIndex)
                  }}
                />
              );

              return (
                <Popup
                  popupContent={<HighlightPopup comment={highlight.comment} />}
                  onMouseOver={(popupContent) =>
                    setTip(highlight, (highlight) => popupContent)
                  }
                  onMouseOut={hideTip}
                  key={index}
                  children={component}
                />
              );
            }}
            highlights={highlights}
          />
        )}
      </PdfLoader>
    </div>
  );
};

export default PDFHighlighter;