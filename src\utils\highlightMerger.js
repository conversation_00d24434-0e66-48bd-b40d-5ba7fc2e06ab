/**
 * Utility functions for handling overlapping highlights by splitting and merging them
 */

/**
 * Check if two rectangles overlap
 */
const doRectsOverlap = (rect1, rect2) => {
  return !(
    rect1.x2 < rect2.x1 || 
    rect2.x2 < rect1.x1 || 
    rect1.y2 < rect2.y1 || 
    rect2.y2 < rect1.y1
  );
};

/**
 * Calculate the intersection of two rectangles
 */
const getIntersection = (rect1, rect2) => {
  if (!doRectsOverlap(rect1, rect2)) return null;
  
  return {
    x1: Math.max(rect1.x1, rect2.x1),
    y1: Math.max(rect1.y1, rect2.y1),
    x2: Math.min(rect1.x2, rect2.x2),
    y2: Math.min(rect1.y2, rect2.y2)
  };
};

/**
 * Split a highlight rectangle by removing an overlapping area
 */
const splitHighlightRect = (originalRect, overlapRect) => {
  const intersection = getIntersection(originalRect, overlapRect);
  if (!intersection) return [originalRect];
  
  // Split rectangle into non-overlapping segments
  
  const segments = [];
  
  // For horizontal text highlights, we primarily care about left and right segments
  
  // Left segment (text before the overlap)
  if (originalRect.x1 < intersection.x1) {
    const leftSegment = {
      ...originalRect,
      x2: intersection.x1
    };
    // Add left segment
    segments.push(leftSegment);
  }
  
  // Right segment (text after the overlap)
  if (originalRect.x2 > intersection.x2) {
    const rightSegment = {
      ...originalRect,
      x1: intersection.x2
    };
    // Add right segment
    segments.push(rightSegment);
  }
  
  // Only add vertical segments if there's significant vertical separation
  const verticalThreshold = 2; // pixels
  
  // Top segment (if there's vertical space above)
  if (originalRect.y1 < intersection.y1 - verticalThreshold) {
    const topSegment = {
      ...originalRect,
      y2: intersection.y1,
      x1: Math.max(originalRect.x1, intersection.x1),
      x2: Math.min(originalRect.x2, intersection.x2)
    };
    // Add top segment
    segments.push(topSegment);
  }
  
  // Bottom segment (if there's vertical space below)
  if (originalRect.y2 > intersection.y2 + verticalThreshold) {
    const bottomSegment = {
      ...originalRect,
      y1: intersection.y2,
      x1: Math.max(originalRect.x1, intersection.x1),
      x2: Math.min(originalRect.x2, intersection.x2)
    };
    // Add bottom segment
    segments.push(bottomSegment);
  }
  
  const validSegments = segments.filter(seg => seg.x2 > seg.x1 && seg.y2 > seg.y1);
  // Return valid segments
  
  return validSegments;
};

/**
 * Process highlights to handle overlaps by creating non-overlapping segments
 * Newer highlights take precedence over older ones
 */
export const processOverlappingHighlights = (highlights) => {
  // Process highlights for overlaps
  
  if (!highlights || highlights.length === 0) return [];
  
  // Filter out any highlights that are already segments to prevent infinite loops
  // But keep split highlights as they are the result of previous processing
  const processableHighlights = highlights.filter(h => !h.id.includes('_segment_'));
  
  if (processableHighlights.length === 0) {
    console.log('No processable highlights found, returning as-is');
    return highlights;
  }
  
  // Sort highlights by creation time (oldest first)
  const sortedHighlights = [...processableHighlights].sort((a, b) => {
    return new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime();
  });
  
  // Sort highlights by creation time
  
  const processedHighlights = [];
  
  sortedHighlights.forEach((currentHighlight, index) => {
    // Process current highlight for overlaps
    
    if (!currentHighlight.position?.boundingRect) {
      // No bounding rect, add as-is
      processedHighlights.push(currentHighlight);
      return;
    }
    
    let currentRects = [currentHighlight.position.boundingRect];
    let hasOverlaps = false;
    
    // Check against all newer highlights (avoid self-comparison)
    for (let i = index + 1; i < sortedHighlights.length; i++) {
      const newerHighlight = sortedHighlights[i];
      
      // Skip self-comparison
      if (currentHighlight.id === newerHighlight.id) {
        console.log('  Skipping self-comparison');
        continue;
      }
      
      console.log(`Checking overlap between:`);
      console.log(`  Current (${currentHighlight.content?.text}):`, currentHighlight.position.boundingRect);
      console.log(`  Newer (${newerHighlight.content?.text}):`, newerHighlight.position?.boundingRect);
      
      if (!newerHighlight.position?.boundingRect) {
        console.log('  Newer highlight has no bounding rect, skipping');
        continue;
      }
      
      // Skip if on different pages
      if (currentHighlight.position.pageNumber !== newerHighlight.position.pageNumber) {
        console.log('  Different pages, skipping');
        continue;
      }
      
      const overlaps = doRectsOverlap(currentHighlight.position.boundingRect, newerHighlight.position.boundingRect);
      console.log('  Overlaps:', overlaps);
      
      if (overlaps) {
        hasOverlaps = true;
        // Split current rectangles by removing overlap with newer highlight
        currentRects = currentRects.flatMap(rect => 
          splitHighlightRect(rect, newerHighlight.position.boundingRect)
        );
        // Split into segments
      }
    }
    
    // Add segments for current highlight
    if (hasOverlaps && currentRects.length > 0) {
      // Only create segments if there were overlaps
      currentRects.forEach((rect, rectIndex) => {
        processedHighlights.push({
          ...currentHighlight,
          id: `${currentHighlight.id}_segment_${rectIndex}`,
          originalId: currentHighlight.id,
          position: {
            ...currentHighlight.position,
            boundingRect: rect,
            rects: [rect] // Update rects array to match the new boundingRect
          }
        });
      });
    } else {
      // No overlaps, add original highlight
      processedHighlights.push(currentHighlight);
    }
  });
  
  console.log(`Processed ${processedHighlights.length} highlights with overlaps resolved`);
  
  return processedHighlights;
};

/**
 * Check if a highlight should be split based on overlaps with newer highlights
 */
export const shouldSplitHighlight = (highlight, allHighlights) => {
  const highlightTime = new Date(highlight.createdAt || 0).getTime();
  
  return allHighlights.some(other => {
    if (other.id === highlight.id) return false;
    if (other.position?.pageNumber !== highlight.position?.pageNumber) return false;
    
    const otherTime = new Date(other.createdAt || 0).getTime();
    if (otherTime <= highlightTime) return false; // Only newer highlights can split
    
    return doRectsOverlap(highlight.position.boundingRect, other.position.boundingRect);
  });
};

/**
 * Get the effective color for a highlight at a specific position
 * considering overlapping highlights (newer highlights take precedence)
 */
export const getEffectiveHighlightColor = (position, allHighlights) => {
  const overlappingHighlights = allHighlights.filter(highlight => {
    if (!highlight.position?.boundingRect) return false;
    if (highlight.position.pageNumber !== position.pageNumber) return false;
    
    return doRectsOverlap(highlight.position.boundingRect, position.boundingRect);
  });
  
  if (overlappingHighlights.length === 0) return null;
  
  // Sort by creation time and return the newest one's color
  const newest = overlappingHighlights.sort((a, b) => {
    const timeA = new Date(a.createdAt || 0).getTime();
    const timeB = new Date(b.createdAt || 0).getTime();
    return timeB - timeA;
  })[0];
  
  return newest.color;
};
