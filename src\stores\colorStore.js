// src/stores/colorStore.js
import { create } from 'zustand';

// Define default color (matching your yellow color from HIGHLIGHT_COLORS)
const DEFAULT_COLOR = {
    id: 'yellow',
    name: 'Yellow',
    backgroundColor: '#ffeb3b',
    color: '#f57f17'
};

export const useColorStore = create((set) => ({
    selectedColor: DEFAULT_COLOR,
    setSelectedColor: (color) => set({ selectedColor: color }),
}));
