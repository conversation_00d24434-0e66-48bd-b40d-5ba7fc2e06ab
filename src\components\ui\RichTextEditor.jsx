import React, { use<PERSON><PERSON>back, useEffect, useMemo, useState } from "react";
import {
  createEditor,
  Editor,
  Range,
  Element as SlateElement,
  Transforms,
} from "slate";
import { withHistory } from "slate-history";
import { Editable, ReactEditor, Slate, useSlate, withReact } from "slate-react";
import "../../styles/RichTextEditor.css";

// Define custom types for our editor
const LIST_TYPES = ["bulleted-list"];
const TEXT_ALIGN_TYPES = ["left", "center", "right", "justify"];

// Helper functions for formatting
const isBlockActive = (editor, format, blockType = "type") => {
  const { selection } = editor;
  if (!selection) return false;

  const [match] = Array.from(
    Editor.nodes(editor, {
      at: Editor.unhangRange(editor, selection),
      match: (n) =>
        !Editor.isEditor(n) &&
        SlateElement.isElement(n) &&
        n[blockType] === format,
    })
  );

  return !!match;
};

// Fixed mark detection that properly handles cursor state and removal
const isMarkActive = (editor, format) => {
  try {
    const marks = Editor.marks(editor);
    return marks && marks[format] === true;
  } catch (error) {
    return false;
  }
};

const toggleBlock = (editor, format) => {
  // Always focus the editor first
  try {
    ReactEditor.focus(editor);
  } catch (error) {
    // Fallback: force focus by selecting the editor
    const editorElement = ReactEditor.toDOMNode(editor, editor);
    if (editorElement) {
      editorElement.focus();
    }
  }

  const isActive = isBlockActive(
    editor,
    format,
    TEXT_ALIGN_TYPES.includes(format) ? "align" : "type"
  );
  const isList = LIST_TYPES.includes(format);

  Transforms.unwrapNodes(editor, {
    match: (n) =>
      !Editor.isEditor(n) &&
      SlateElement.isElement(n) &&
      LIST_TYPES.includes(n.type),
    split: true,
  });

  let newProperties;
  if (TEXT_ALIGN_TYPES.includes(format)) {
    newProperties = {
      align: isActive ? undefined : format,
    };
  } else {
    newProperties = {
      type: isActive ? "paragraph" : isList ? "list-item" : format,
    };
  }

  Transforms.setNodes(editor, newProperties);

  if (!isActive && isList) {
    const block = { type: format, children: [] };
    Transforms.wrapNodes(editor, block);
  }
};

const toggleMark = (editor, format) => {
  // Always focus the editor first
  try {
    ReactEditor.focus(editor);
  } catch (error) {
    // Fallback: force focus by selecting the editor
    try {
      const editorElement = ReactEditor.toDOMNode(editor, editor);
      if (editorElement) {
        editorElement.focus();
      }
    } catch (domError) {
      // If DOM approach fails, ensure we have a selection
      if (!editor.selection) {
        Transforms.select(editor, Editor.start(editor, []));
      }
    }
  }

  // Ensure we have a selection after focusing
  if (!editor.selection) {
    Transforms.select(editor, Editor.start(editor, []));
  }

  const isActive = isMarkActive(editor, format);

  if (isActive) {
    Editor.removeMark(editor, format);
  } else {
    Editor.addMark(editor, format, true);
  }

  // Force editor to update by triggering a change
  editor.onChange();
};

// Toolbar button component
const Button = ({ active, onMouseDown, children, title }) => (
  <button
    className={`rich-text-btn ${active ? "active" : ""}`}
    onMouseDown={onMouseDown}
    title={title}
    type="button"
  >
    {children}
  </button>
);

// Enhanced Mark button with improved state management
const MarkButton = ({ format, children, title }) => {
  const editor = useSlate();
  const [isActive, setIsActive] = useState(false);

  // Update active state when editor changes
  useEffect(() => {
    setIsActive(isMarkActive(editor, format));
  }, [editor, format, editor.selection, editor.marks]);

  const handleMouseDown = useCallback(
    (event) => {
      event.preventDefault();
      event.stopPropagation();

      // Add a small delay to ensure DOM is ready
      setTimeout(() => {
        toggleMark(editor, format);
        // Update state immediately after toggle
        setIsActive(isMarkActive(editor, format));
      }, 0);
    },
    [editor, format]
  );

  return (
    <Button active={isActive} onMouseDown={handleMouseDown} title={title}>
      {children}
    </Button>
  );
};

// Block button (Lists)
const BlockButton = ({ format, children, title }) => {
  const editor = useSlate();
  const isActive = isBlockActive(editor, format);

  const handleMouseDown = useCallback(
    (event) => {
      event.preventDefault();
      // Add a small delay to ensure DOM is ready
      setTimeout(() => {
        toggleBlock(editor, format);
      }, 0);
    },
    [editor, format]
  );

  return (
    <Button active={isActive} onMouseDown={handleMouseDown} title={title}>
      {children}
    </Button>
  );
};

// Toolbar component
const Toolbar = ({ children }) => (
  <div className="rich-text-toolbar">{children}</div>
);

// Element renderer
const RichTextElement = ({ attributes, children, element }) => {
  const style = { textAlign: element.align };

  switch (element.type) {
    case "bulleted-list":
      return (
        <ul style={style} {...attributes} className="slate-bulleted-list">
          {children}
        </ul>
      );
    case "list-item":
      return (
        <li style={style} {...attributes} className="slate-list-item">
          {children}
        </li>
      );
    default:
      return (
        <p style={style} {...attributes} className="slate-paragraph">
          {children}
        </p>
      );
  }
};

// Leaf renderer for text formatting
const Leaf = ({ attributes, children, leaf }) => {
  if (leaf.bold) {
    children = <strong>{children}</strong>;
  }

  if (leaf.italic) {
    children = <em>{children}</em>;
  }

  if (leaf.underline) {
    children = <u>{children}</u>;
  }

  return <span {...attributes}>{children}</span>;
};

// Enhanced withMarks plugin to handle mark persistence and state updates
const withMarks = (editor) => {
  const { deleteBackward, deleteForward, insertText, onChange } = editor;

  // Override onChange to trigger mark button updates
  editor.onChange = () => {
    onChange();
    // Force re-render of mark buttons by triggering a state update
    const marks = Editor.marks(editor);
    // This will trigger useEffect in MarkButton components
    editor.marks = marks;
  };

  // Override deleteBackward to maintain marks when deleting all formatted text
  editor.deleteBackward = (...args) => {
    const { selection } = editor;

    if (selection && Range.isCollapsed(selection)) {
      // Get current marks before deletion
      const currentMarks = Editor.marks(editor);

      // Perform the deletion
      deleteBackward(...args);

      // If we had marks and the selection is still collapsed, restore them
      if (currentMarks && Object.keys(currentMarks).length > 0) {
        const newSelection = editor.selection;
        if (newSelection && Range.isCollapsed(newSelection)) {
          // Add back the marks so they persist for new text
          Object.entries(currentMarks).forEach(([key, value]) => {
            if (value) {
              Editor.addMark(editor, key, value);
            }
          });
        }
      }
    } else {
      deleteBackward(...args);
    }
  };

  // Override deleteForward with similar logic
  editor.deleteForward = (...args) => {
    const { selection } = editor;

    if (selection && Range.isCollapsed(selection)) {
      const currentMarks = Editor.marks(editor);
      deleteForward(...args);

      if (currentMarks && Object.keys(currentMarks).length > 0) {
        const newSelection = editor.selection;
        if (newSelection && Range.isCollapsed(newSelection)) {
          Object.entries(currentMarks).forEach(([key, value]) => {
            if (value) {
              Editor.addMark(editor, key, value);
            }
          });
        }
      }
    } else {
      deleteForward(...args);
    }
  };

  return editor;
};

// Helper function to parse value
const parseValue = (value) => {
  if (!value || (typeof value === "string" && !value.trim())) {
    return [
      {
        type: "paragraph",
        children: [{ text: "" }],
      },
    ];
  }

  if (typeof value === "string") {
    try {
      // Try to parse as JSON (Slate format)
      const parsed = JSON.parse(value);
      return Array.isArray(parsed)
        ? parsed
        : [
          {
            type: "paragraph",
            children: [{ text: value }],
          },
        ];
    } catch {
      // If not JSON, create a simple paragraph with the text
      return [
        {
          type: "paragraph",
          children: [{ text: value }],
        },
      ];
    }
  }

  return Array.isArray(value)
    ? value
    : [
      {
        type: "paragraph",
        children: [{ text: "" }],
      },
    ];
};

const RichTextEditor = ({
  value,
  onChange,
  placeholder = "Write your notes here...",
}) => {
  // Create editor with our custom plugin
  const editor = useMemo(
    () => withMarks(withHistory(withReact(createEditor()))),
    []
  );

  // Parse and initialize editor value
  const [editorValue, setEditorValue] = useState(() => parseValue(value));

  // Update editor value when prop changes (for page switching)
  useEffect(() => {
    const newValue = parseValue(value);
    setEditorValue(newValue);

    // Reset editor content when value changes
    if (editor.children !== newValue) {
      // Clear the editor and set new content
      Transforms.delete(editor, {
        at: {
          anchor: Editor.start(editor, []),
          focus: Editor.end(editor, []),
        },
      });

      // Insert new content
      Transforms.insertNodes(editor, newValue, { at: [0] });

      // Remove the empty paragraph that might be left
      if (editor.children.length > newValue.length) {
        Transforms.removeNodes(editor, { at: [newValue.length] });
      }
    }
  }, [value, editor]);

  // Handle editor changes with improved state management
  const handleChange = useCallback(
    (newValue) => {
      setEditorValue(newValue);

      if (onChange) {
        // Convert to JSON string for storage
        const serialized = JSON.stringify(newValue);
        onChange({ target: { value: serialized } });
      }
    },
    [onChange]
  );

  // Handle keyboard shortcuts
  const handleKeyDown = useCallback(
    (event) => {
      if (!event.ctrlKey && !event.metaKey) {
        return;
      }

      switch (event.key) {
        case "b": {
          event.preventDefault();
          toggleMark(editor, "bold");
          break;
        }
        case "i": {
          event.preventDefault();
          toggleMark(editor, "italic");
          break;
        }
        case "u": {
          event.preventDefault();
          toggleMark(editor, "underline");
          break;
        }
        default:
          break;
      }
    },
    [editor]
  );

  // Render element
  const renderElement = useCallback(
    (props) => <RichTextElement {...props} />,
    []
  );

  // Render leaf
  const renderLeaf = useCallback((props) => <Leaf {...props} />, []);

  return (
    <div className="rich-text-editor-container">
      <Slate
        editor={editor}
        initialValue={editorValue}
        onChange={handleChange}
        key={JSON.stringify(editorValue)} // Force re-render when value changes
      >
        <Toolbar>
          <span className="toolbar-title">Notes:</span>

          <MarkButton format="bold" title="Bold (Ctrl+B)">
            <strong>B</strong>
          </MarkButton>
          <MarkButton format="italic" title="Italic (Ctrl+I)">
            <em>I</em>
          </MarkButton>
          <MarkButton format="underline" title="Underline (Ctrl+U)">
            <u>U</u>
          </MarkButton>
          <BlockButton format="bulleted-list" title="Bullet Points">
            •
          </BlockButton>
        </Toolbar>
        <hr className="textarea-separator" />
        <Editable
          className="rich-text-editor-content"
          renderElement={renderElement}
          renderLeaf={renderLeaf}
          placeholder={placeholder}
          onKeyDown={handleKeyDown}
          spellCheck
          autoFocus
        />
      </Slate>
    </div>
  );
};

export default RichTextEditor;
