import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { useAuthStore } from './authStore'; // Import the auth store

/**
 * Highlight Store for managing PDF text highlights using react-pdf-highlighter
 * Stores viewport-independent highlight positions with vector coordinates
 * Compatible with react-pdf-highlighter's IHighlight interface
 *
 * Highlight structure:
 * {
 *   id: string,
 *   userId: string,
 *   position: {
 *     boundingRect: {
 *       x1: number,
 *       y1: number,
 *       x2: number,
 *       y2: number,
 *       width: number,
 *       height: number,
 *       pageNumber: number
 *     },
 *     rects: [{
 *       x1: number,
 *       y1: number,
 *       x2: number,
 *       y2: number,
 *       width: number,
 *       height: number,
 *       pageNumber: number
 *     }],
 *     pageNumber: number
 *   },
 *   comment: {
 *     text: string,
 *     emoji: string
 *   }
 * }
 */
export const useHighlightStore = create(
  persist(
    (set, get) => ({
      _hasHydrated: false,
      setHasHydrated: (state) => set({ _hasHydrated: state }),
      version: 1,
      highlights: {}, // Structure: { userId: { pageId: { topicId: [IHighlight[]] } } }
      isHighlightMode: true,

      setHighlightMode: (enabled) => set({ isHighlightMode: enabled }),

      addHighlight: (pageId, topicId, highlight) => {
        const currentUser = useAuthStore.getState().currentUser;
        if (!currentUser) {
          console.warn('No authenticated user found');
          return null;
        }

        const userId = currentUser.id;
        const vectorHighlight = {
          ...highlight,
          userId, // Add userId to the highlight
          createdAt: highlight.createdAt || new Date().toISOString(), // Ensure createdAt is set
          content: highlight.content, // Preserve content with selected text
          position: {
            boundingRect: {
              x1: highlight.position.boundingRect.x1,
              y1: highlight.position.boundingRect.y1,
              x2: highlight.position.boundingRect.x2,
              y2: highlight.position.boundingRect.y2,
              width: highlight.position.boundingRect.width || 0,
              height: highlight.position.boundingRect.height || 0,
              pageNumber: highlight.position.boundingRect.pageNumber
            },
            rects: highlight.position.rects.map(rect => ({
              x1: rect.x1,
              y1: rect.y1,
              x2: rect.x2,
              y2: rect.y2,
              width: rect.width || 0,
              height: rect.height || 0,
              pageNumber: rect.pageNumber
            })),
            pageNumber: highlight.position.pageNumber
          },
          comment: {
            ...highlight.comment,
            text: highlight.comment?.text || "",
            emoji: highlight.comment?.emoji || ""
          }
        };

        set((state) => {
          const newState = {
            highlights: {
              ...state.highlights,
              [userId]: {
                ...state.highlights[userId],
                [pageId]: {
                  ...state.highlights[userId]?.[pageId],
                  [topicId]: [
                    ...(state.highlights[userId]?.[pageId]?.[topicId] || []),
                    vectorHighlight
                  ]
                }
              }
            }
          };

          console.log('Highlight added to store:', {
            userId,
            pageId,
            topicId,
            highlightId: vectorHighlight.id,
            createdAt: vectorHighlight.createdAt,
            hasContent: !!vectorHighlight.content,
            contentText: vectorHighlight.content?.text?.substring(0, 50) + '...',
            totalHighlights: newState.highlights[userId][pageId][topicId].length
          });

          return newState;
        });

        return vectorHighlight.id;
      },

      getHighlights: (pageId, topicId) => {
        const currentUser = useAuthStore.getState().currentUser;
        if (!currentUser) {
          return [];
        }

        const { highlights } = get();
        const userId = currentUser.id;
        return highlights[userId]?.[pageId]?.[topicId] || [];
      },

      updateHighlight: (pageId, topicId, highlightId, position, content) => {
        const currentUser = useAuthStore.getState().currentUser;
        if (!currentUser) {
          console.warn('No authenticated user found');
          return;
        }

        const userId = currentUser.id;
        set((state) => ({
          highlights: {
            ...state.highlights,
            [userId]: {
              ...state.highlights[userId],
              [pageId]: {
                ...state.highlights[userId]?.[pageId],
                [topicId]: (state.highlights[userId]?.[pageId]?.[topicId] || []).map(h =>
                  h.id === highlightId
                    ? {
                      ...h,
                      position: position ? {
                        boundingRect: {
                          x1: position.boundingRect.x1,
                          y1: position.boundingRect.y1,
                          x2: position.boundingRect.x2,
                          y2: position.boundingRect.y2,
                          width: position.boundingRect.width || h.position.boundingRect.width,
                          height: position.boundingRect.height || h.position.boundingRect.height,
                          pageNumber: position.boundingRect.pageNumber
                        },
                        rects: position.rects.map(rect => ({
                          x1: rect.x1,
                          y1: rect.y1,
                          x2: rect.x2,
                          y2: rect.y2,
                          width: rect.width || h.position.rects[0].width,
                          height: rect.height || h.position.rects[0].height,
                          pageNumber: rect.pageNumber
                        })),
                        pageNumber: position.pageNumber
                      } : h.position,
                      content: content ? { ...h.content, ...content } : h.content
                    }
                    : h
                )
              }
            }
          }
        }));
      },

      removeHighlight: (pageId, topicId, highlightId) => {
        const currentUser = useAuthStore.getState().currentUser;
        if (!currentUser) {
          console.warn('No authenticated user found');
          return;
        }

        const userId = currentUser.id;
        set((state) => ({
          highlights: {
            ...state.highlights,
            [userId]: {
              ...state.highlights[userId],
              [pageId]: {
                ...state.highlights[userId]?.[pageId],
                [topicId]: (state.highlights[userId]?.[pageId]?.[topicId] || []).filter(
                  h => h.id !== highlightId
                )
              }
            }
          }
        }));
      },

      clearHighlights: (pageId, topicId) => {
        const currentUser = useAuthStore.getState().currentUser;
        if (!currentUser) {
          console.warn('No authenticated user found');
          return;
        }

        const userId = currentUser.id;
        set((state) => ({
          highlights: {
            ...state.highlights,
            [userId]: {
              ...state.highlights[userId],
              [pageId]: {
                ...state.highlights[userId]?.[pageId],
                [topicId]: []
              }
            }
          }
        }));
      },

      getAllHighlights: () => {
        const currentUser = useAuthStore.getState().currentUser;
        if (!currentUser) {
          return {};
        }

        const { highlights } = get();
        const userId = currentUser.id;
        return highlights[userId] || {};
      },

      getUserHighlights: (userId) => {
        const { highlights } = get();
        return highlights[userId] || {};
      },

      importHighlights: (highlightData) => {
        set({ highlights: highlightData });
      }
    }),
    {
      name: 'highlight-storage',
      version: 1,
      onRehydrateStorage: () => (state) => {
        // This callback is called when the store is rehydrated
        if (state) {
          state.setHasHydrated(true);
        }
      }
    }
  )
);

// This is important: Subscribe to hydration to ensure the store is ready
if (typeof window !== 'undefined') {
  useHighlightStore.persist.onFinishHydration((state) => {
    state.setHasHydrated(true);
  });
}