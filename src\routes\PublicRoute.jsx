import { Navigate } from 'react-router-dom';
import { useAuthStore } from '../stores/authStore';

/**
 * PublicRoute component - Only accessible when NOT authenticated
 * Redirects to reading screen if user is already authenticated
 */
const PublicRoute = ({ children }) => {
  const isAuthenticated = useAuthStore((state) => state.isAuthenticated);

  return !isAuthenticated ? children : <Navigate to="/readingScreen" replace />;
};

export default PublicRoute;
