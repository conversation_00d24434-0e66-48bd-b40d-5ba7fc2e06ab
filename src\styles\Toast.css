.tools-panel-sidebar {
  width: 100%;
  height: 100%;
  background-color: #fffbf3;
  padding: 20px;
  border-left: 1px solid #e5e2d9;
  box-sizing: border-box;
  overflow-y: auto;
  /* Ensures this panel is independently scrollable */
  overflow-x: hidden;
  position: relative;
  font-family: inherit;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.tools-panel-sidebar::-webkit-scrollbar {
  display: none;
}

.tool-section {
  margin-bottom: 24px;
}

.section-header {
  margin-bottom: 12px;
}

.section-title {
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

.pagination-controls {
  display: flex;
  align-items: stretch;
  border: 1px solid #d0c9b6;
  border-radius: 8px;
  overflow: hidden;
}

.pagination-controls>*:not(:last-child) {
  border-right: 1px solid #d0c9b6;
}

.pagination-nav-btn,
.pagination-btn {
  all: unset;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8px 12px;
  font-size: 14px;
  color: #333;
  cursor: pointer;
  height: 100%;
  box-sizing: border-box;
}

.pagination-nav-btn {
  font-size: 14px;
}

.pagination-btn.active {
  font-weight: bold;
  background-color: #333;
  color: white;
  border-radius: 0;
  height: 100%;
}

.text-size-controls {
  display: flex;
  border: 1px solid #d0c9b6;
  border-radius: 8px;
  overflow: hidden;
}

.text-size-controls>*:not(:last-child) {
  border-right: 1px solid #d0c9b6;
}

.text-size-btn {
  flex: 1;
  cursor: pointer;
  border: none;
  background: none;
  font-size: 14px;
  color: #333;
  padding: 4px 8px;
}

.text-size-btn.active {
  font-weight: bold;
  background-color: #333;
  color: white;
}

.page-controls-wrapper {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.add-bookmark-btn {
  width: 38px;
  height: 38px;
  border: 1px solid #d0c9b6;
  border-radius: 8px;
  background: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.add-bookmark-btn:hover {
  background-color: #f8f6f0;
}

.media-player {
  border: 1px solid #d0c9b6;
  border-radius: 20px;
  padding: 10px 16px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.media-controls {
  display: flex;
  gap: 16px;
  align-items: center;
}

.media-control-btn {
  background: transparent;
  border: none;
  cursor: pointer;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  fill: #333;
  font-size: 18px;
}

.annotate-controls {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.color-picker-section {
  display: flex;
  flex-direction: column;
  gap: 8px;
  align-items: center;
}

.notes-section .rich-text-editor-content {
  border: 1px solid #e5e2d9;
  border-radius: 10px;
  padding: 12px;
  font-size: 14px;
  line-height: 1.5;
  color: #333;
  min-height: 100px;
  /* Give it a minimum height */
}

.notes-section .rich-text-editor-content:focus {
  outline: 1px solid #aaa;
}

.notes-section .rich-text-editor-content:empty::before {
  content: "Write your notes here....";
  color: #aaa;
  cursor: text;
  pointer-events: none;
}