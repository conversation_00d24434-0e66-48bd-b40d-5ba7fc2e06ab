import { create } from 'zustand';
import { persist } from 'zustand/middleware';

// Hardcoded user credentials for demo
const USERS = [
  { id: 'user1', email: '<EMAIL>', password: 'pass1' },
  { id: 'user2', email: '<EMAIL>', password: 'pass2' }
];

export const useAuthStore = create(
  persist(
    (set) => ({
      currentUser: null,
      isAuthenticated: false,

      login: (email, password) => {
        const user = USERS.find(
          (u) => u.email === email && u.password === password
        );

        if (user) {
          set({ currentUser: user, isAuthenticated: true });
          return true;
        }
        return false;
      },

      logout: () => {
        set({ currentUser: null, isAuthenticated: false });
      }
    }),
    {
      name: 'auth-storage'
    }
  )
);