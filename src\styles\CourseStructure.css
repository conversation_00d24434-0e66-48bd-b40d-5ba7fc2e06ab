.course-structure-sidebar {
  width: 100%;
  height: 100%;
  background-color: #fffbf3;
  padding: 24px;
  border-right: 1px solid #e0e0e0;
  box-sizing: border-box;
  overflow-y: auto;
  /* Ensures this panel is independently scrollable */
  overflow-x: hidden;
  scroll-behavior: smooth;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.course-structure-sidebar::-webkit-scrollbar {
  display: none;
}

.bookmarks-btn {
  width: 100%;
  padding: 12px;
  background-color: #333;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: bold;
  cursor: pointer;
  margin-bottom: 30px;
}

.course-content h2 {
  font-size: 18px;
  color: #555;
  margin-bottom: 20px;
}

.topic-section h3 {
  font-size: 16px;
  color: #333;
  margin-bottom: 10px;
}

.topic-header {
  cursor: pointer;
  user-select: none;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.topic-header:hover {
  color: #000;
}

.topic-section ul {
  list-style: none;
  padding-left: 10px;
  margin: 0;
}

.topic-section li {
  padding: 8px 0;
  cursor: pointer;
  color: #666;
  font-size: 14px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.topic-section li:hover {
  color: #000;
}

.topic-section li.active {
  font-weight: bold;
  color: #000;
}

.topic-title {
  flex: 1;
}

.page-indicator {
  font-size: 12px;
  color: #888;
  background-color: rgba(255, 255, 255, 0.6);
  padding: 2px 6px;
  border-radius: 10px;
  margin-left: 8px;
  border: 1px solid #e0e0e0;
}